import React, { useState, useEffect, useMemo, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Play, AlertTriangle, Loader2, Shield } from 'lucide-react';
import {
  decodeVideoLinks,
  parseVideoLinks,
  generatePlayerNames,
  isValidVideoLink,
  extractVideoUrl,
  isSecureVideoLink,
  getIFrameConfig,
  getIFrameConfigDescription,
  detectVideoPlatform
} from '@/utils/videoSecurity';
import {
  getAspectRatioConfig,
  AspectRatioOptions
} from '@/utils/aspectRatio';
import { validateIframeSrc, getSecureIframeAttributes } from '@/utils/securityHeaders';
import {
  detectAdBlockers,
  quickAdBlockerCheck,
  AdBlockerDetectionResult
} from '@/utils/adBlockerDetection';
import AdBlockerDetectionModal from '@/components/AdBlockerDetectionModal';

// SecureIframe component that dynamically loads src to hide URLs from browser source
interface SecureIframeProps {
  src: string;
  title: string;
  className: string;
  allowFullScreen: boolean;
  allow: string;
  loading?: string;
  referrerPolicy: string;
  sandbox: string;
  onLoad?: () => void;
  onError?: () => void;
}

function SecureIframe({
  src,
  title,
  className,
  allowFullScreen,
  allow,
  loading,
  referrerPolicy,
  sandbox,
  onLoad,
  onError
}: SecureIframeProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    // Dynamically set the src to hide it from static HTML source
    if (iframeRef.current && src) {
      iframeRef.current.src = src;
    }
  }, [src]);

  const iframeProps: any = {
    ref: iframeRef,
    title,
    className,
    allowFullScreen,
    allow,
    loading,
    referrerPolicy,
    onLoad,
    onError
  };

  // Only add sandbox if it's not empty (to prevent sandbox restrictions)
  if (sandbox && sandbox.trim() !== '') {
    iframeProps.sandbox = sandbox;
  }

  return <iframe {...iframeProps} />;
}

interface SecureVideoPlayerProps {
  /** Encoded video links string */
  encodedVideoLinks?: string;
  /** Fallback for legacy videoLinks field */
  legacyVideoLinks?: string;
  /** Title for the player */
  title?: string;
  /** Additional CSS classes */
  className?: string;
  /** Whether to show player selection buttons */
  showPlayerSelection?: boolean;
  /** Enable dynamic aspect ratio detection (default: false for backward compatibility) */
  enableDynamicAspectRatio?: boolean;
  /** Force mobile-friendly aspect ratios */
  forceMobileFriendly?: boolean;
  /** Override aspect ratio detection with specific ratio */
  preferredAspectRatio?: string;
  /** Enable responsive aspect ratio adjustments */
  enableResponsive?: boolean;
}

export default function SecureVideoPlayer({
  encodedVideoLinks,
  legacyVideoLinks,
  title = "Video Player",
  className = "",
  showPlayerSelection = true,
  enableDynamicAspectRatio = false,
  forceMobileFriendly = false,
  preferredAspectRatio,
  enableResponsive = true
}: SecureVideoPlayerProps) {
  const [selectedPlayerIndex, setSelectedPlayerIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Ad blocker detection states
  const [isCheckingAdBlocker, setIsCheckingAdBlocker] = useState(true);
  const [adBlockerDetected, setAdBlockerDetected] = useState(false);
  const [adBlockerResult, setAdBlockerResult] = useState<AdBlockerDetectionResult | null>(null);
  const [showAdBlockerModal, setShowAdBlockerModal] = useState(false);
  const [videoBlocked, setVideoBlocked] = useState(false);

  // Ad blocker detection effect
  useEffect(() => {
    const performAdBlockerCheck = async () => {
      try {
        setIsCheckingAdBlocker(true);

        // Quick check first
        const quickCheck = await quickAdBlockerCheck();

        if (quickCheck) {
          // If quick check detects ad blocker, run full detection
          const fullResult = await detectAdBlockers();

          if (fullResult.hasAdBlocker) {
            setAdBlockerDetected(true);
            setAdBlockerResult(fullResult);
            setVideoBlocked(true);
            setShowAdBlockerModal(true);
          }
        }
      } catch (error) {
        console.error('Ad blocker detection failed:', error);
        // On detection failure, allow video to play (graceful fallback)
      } finally {
        setIsCheckingAdBlocker(false);
      }
    };

    // Only run detection if we have video links
    if (encodedVideoLinks || legacyVideoLinks) {
      performAdBlockerCheck();
    } else {
      setIsCheckingAdBlocker(false);
    }
  }, [encodedVideoLinks, legacyVideoLinks]);

  // Decode and parse video links (separate from current URL calculation)
  const videoLinksData = useMemo(() => {
    try {
      setIsLoading(true);
      setError(null);

      let rawLinks = '';

      // Try to decode secure links first, fallback to legacy
      if (encodedVideoLinks) {
        rawLinks = decodeVideoLinks(encodedVideoLinks);
      } else if (legacyVideoLinks) {
        rawLinks = legacyVideoLinks;
        console.warn('Using legacy video links - consider updating to secure format');
      }

      if (!rawLinks) {
        setError('No video links available');
        return { links: [], playerNames: [] };
      }

      const links = parseVideoLinks(rawLinks);

      if (links.length === 0) {
        setError('No valid video links found');
        return { links: [], playerNames: [] };
      }

      // Validate and filter links with detailed logging
      const validLinks = links.filter((link, index) => {
        const isValid = isValidVideoLink(link);
        const isSecure = isSecureVideoLink(link);

        if (!isValid) {
          console.warn(`Link ${index + 1} failed validation:`, link.substring(0, 100));
          return false;
        }
        if (!isSecure) {
          console.warn(`Link ${index + 1} failed security check:`, link.substring(0, 100));
          return false;
        }

        console.log(`Link ${index + 1} validated successfully:`, link.substring(0, 50) + '...');
        return true;
      });

      console.log(`Processed ${links.length} links, ${validLinks.length} valid`);

      if (validLinks.length === 0) {
        const errorMsg = links.length > 0
          ? `Found ${links.length} links but none passed validation. Check console for details.`
          : 'No safe video links found';
        setError(errorMsg);
        return { links: [], playerNames: [] };
      }

      const playerNames = generatePlayerNames(validLinks);

      return {
        links: validLinks,
        playerNames
      };
    } catch (err) {
      console.error('Error processing video links:', err);
      setError('Failed to load video player');
      return { links: [], playerNames: [] };
    } finally {
      setIsLoading(false);
    }
  }, [encodedVideoLinks, legacyVideoLinks]);

  // Calculate current URL based on selected player
  const currentUrl = useMemo(() => {
    if (videoLinksData.links.length === 0 || selectedPlayerIndex >= videoLinksData.links.length) {
      return '';
    }
    return extractVideoUrl(videoLinksData.links[selectedPlayerIndex]);
  }, [videoLinksData.links, selectedPlayerIndex]);

  // Calculate aspect ratio configuration for current video
  const aspectRatioConfig = useMemo(() => {
    if (!enableDynamicAspectRatio || !currentUrl) {
      // Return default 16:9 configuration for backward compatibility
      return {
        aspectRatio: { className: 'aspect-video' },
        responsive: { combined: 'aspect-video' },
        platform: 'unknown'
      };
    }

    const options: AspectRatioOptions = {
      forceMobileFriendly,
      preferredRatio: preferredAspectRatio,
      enableResponsive
    };

    return getAspectRatioConfig(currentUrl, options);
  }, [currentUrl, enableDynamicAspectRatio, forceMobileFriendly, preferredAspectRatio, enableResponsive]);

  // Ad blocker handling functions
  const handleAdBlockerRetry = async () => {
    setShowAdBlockerModal(false);
    setIsCheckingAdBlocker(true);

    try {
      const result = await detectAdBlockers();

      if (result.hasAdBlocker) {
        setAdBlockerResult(result);
        setShowAdBlockerModal(true);
      } else {
        setAdBlockerDetected(false);
        setVideoBlocked(false);
        setAdBlockerResult(null);
      }
    } catch (error) {
      console.error('Ad blocker retry failed:', error);
      // On retry failure, allow video to play
      setAdBlockerDetected(false);
      setVideoBlocked(false);
    } finally {
      setIsCheckingAdBlocker(false);
    }
  };

  const handleAdBlockerProceed = () => {
    setShowAdBlockerModal(false);
    setAdBlockerDetected(false);
    setVideoBlocked(false);
    setAdBlockerResult(null);
  };

  const handleAdBlockerModalClose = () => {
    // Don't allow closing the modal if ad blocker is still detected
    // This ensures users must address the ad blocker issue
    if (!adBlockerDetected) {
      setShowAdBlockerModal(false);
    }
  };

  // Calculate platform-specific iFrame configuration with enhanced security
  const iframeConfig = useMemo(() => {
    if (!currentUrl) {
      // Return default configuration if no URL
      return {
        sandbox: "", // No sandbox restrictions for maximum compatibility
        referrerPolicy: "no-referrer-when-downgrade",
        allow: "accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; fullscreen",
        loading: "lazy"
      };
    }

    // Validate iframe source for security
    if (!validateIframeSrc(currentUrl)) {
      console.error('Iframe source failed security validation:', currentUrl);
      return {
        sandbox: "",
        referrerPolicy: "no-referrer",
        allow: "",
        loading: "lazy"
      };
    }

    // Get enhanced security attributes
    const secureAttributes = getSecureIframeAttributes(currentUrl);
    const config = getIFrameConfig(currentUrl);
    const platform = detectVideoPlatform(currentUrl);

    // Merge configurations with security attributes taking precedence
    const finalConfig = {
      ...config,
      ...secureAttributes
    };

    console.log(`🔒 Enhanced iFrame config for ${platform}:`, {
      sandbox: finalConfig.sandbox || 'none (unrestricted)',
      referrerPolicy: finalConfig.referrerPolicy,
      description: getIFrameConfigDescription(finalConfig),
      securityValidated: true
    });

    return finalConfig;
  }, [currentUrl]);

  // Reset selected player when links change
  useEffect(() => {
    if (videoLinksData.links.length > 0 && selectedPlayerIndex >= videoLinksData.links.length) {
      setSelectedPlayerIndex(0);
    }
  }, [videoLinksData.links.length, selectedPlayerIndex]);

  // Ad blocker checking state
  if (isCheckingAdBlocker) {
    return (
      <div className={`bg-background border border-border rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-center space-x-2">
          <Shield className="w-5 h-5 animate-pulse text-primary" />
          <span className="text-muted-foreground">Checking for ad blockers...</span>
        </div>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <div className={`bg-background border border-border rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-center space-x-2">
          <Loader2 className="w-5 h-5 animate-spin" />
          <span className="text-muted-foreground">Loading player...</span>
        </div>
      </div>
    );
  }

  // Error state
  if (error || videoLinksData.links.length === 0) {
    return (
      <div className={`bg-background border border-border rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-center space-x-2 text-destructive">
          <AlertTriangle className="w-5 h-5" />
          <span>{error || 'No video available'}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-background border border-border rounded-lg overflow-hidden ${className}`}>
      {/* Player Selection */}
      {showPlayerSelection && videoLinksData.links.length > 1 && (
        <div className="p-4 border-b border-border bg-muted/50">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-foreground">Select Player</h3>
            <Badge variant="outline" className="text-xs">
              {videoLinksData.links.length} available
            </Badge>
          </div>
          <div className="flex flex-wrap gap-2">
            {videoLinksData.playerNames.map((name, index) => (
              <Button
                key={index}
                variant={selectedPlayerIndex === index ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedPlayerIndex(index)}
                className="text-xs"
              >
                <Play className="w-3 h-3 mr-1" />
                {name}
              </Button>
            ))}
          </div>
        </div>
      )}

      {/* Video Player */}
      <div className="relative">
        <div className={`video-player-container ${enableResponsive ? aspectRatioConfig.responsive.combined : aspectRatioConfig.aspectRatio.className} ${forceMobileFriendly ? 'mobile-friendly' : ''} bg-black`}>
          {videoBlocked ? (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground bg-black/90">
              <div className="text-center p-6">
                <Shield className="w-16 h-16 mx-auto mb-4 text-primary opacity-80" />
                <h3 className="text-lg font-medium text-foreground mb-2">Video Blocked</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Please disable your ad blocker to watch this video
                </p>
                <Button
                  onClick={() => setShowAdBlockerModal(true)}
                  className="bg-primary hover:bg-primary/90 text-primary-foreground"
                >
                  <Shield className="w-4 h-4 mr-2" />
                  Show Instructions
                </Button>
              </div>
            </div>
          ) : currentUrl ? (
            <SecureIframe
              src={currentUrl}
              title={`${title} - ${videoLinksData.playerNames[selectedPlayerIndex] || 'Player'}`}
              className={`video-player-iframe video-player-${aspectRatioConfig.platform} w-full h-full`}
              allowFullScreen
              allow={iframeConfig.allow}
              loading={iframeConfig.loading}
              referrerPolicy={iframeConfig.referrerPolicy as any}
              sandbox={iframeConfig.sandbox}
              onLoad={() => {
                const platform = detectVideoPlatform(currentUrl);
                console.log(`Video player loaded successfully for ${platform} with config:`, getIFrameConfigDescription(iframeConfig));
              }}
              onError={() => {
                const platform = detectVideoPlatform(currentUrl);
                console.error(`Video player failed to load for ${platform}. Config used:`, iframeConfig);
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <div className="text-center">
                <Play className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>Player not available</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Player Info */}
      {title && (
        <div className="p-3 bg-muted/30">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-foreground">{title}</span>
            <div className="flex items-center gap-2">
              {videoLinksData.playerNames[selectedPlayerIndex] && (
                <Badge variant="secondary" className="text-xs">
                  {videoLinksData.playerNames[selectedPlayerIndex]}
                </Badge>
              )}
              {enableDynamicAspectRatio && aspectRatioConfig.platform !== 'unknown' && (
                <Badge variant="outline" className="text-xs">
                  {aspectRatioConfig.platform} • {aspectRatioConfig.aspectRatio.description}
                </Badge>
              )}
              {currentUrl && (
                <Badge variant="secondary" className="text-xs">
                  {getIFrameConfigDescription(iframeConfig)}
                </Badge>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Ad Blocker Detection Modal */}
      {adBlockerResult && (
        <AdBlockerDetectionModal
          isOpen={showAdBlockerModal}
          onClose={handleAdBlockerModalClose}
          detectionResult={adBlockerResult}
          onRetry={handleAdBlockerRetry}
          onProceed={handleAdBlockerProceed}
        />
      )}
    </div>
  );
}

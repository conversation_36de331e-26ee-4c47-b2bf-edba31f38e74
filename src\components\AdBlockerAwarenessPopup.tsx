import React, { useState, useEffect } from 'react';
import { X, Heart, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

interface AdBlockerAwarenessPopupProps {
  isVisible: boolean;
  onDismiss: () => void;
  autoCloseDelay?: number; // in milliseconds
}

const AdBlockerAwarenessPopup: React.FC<AdBlockerAwarenessPopupProps> = ({
  isVisible,
  onDismiss,
  autoCloseDelay = 17000 // 17 seconds default
}) => {
  const [isAnimatingOut, setIsAnimatingOut] = useState(false);

  // Auto-dismiss functionality
  useEffect(() => {
    if (!isVisible) return;

    const autoCloseTimer = setTimeout(() => {
      handleDismiss();
    }, autoCloseDelay);

    return () => clearTimeout(autoCloseTimer);
  }, [isVisible, autoCloseDelay]);

  const handleDismiss = () => {
    setIsAnimatingOut(true);
    // Wait for animation to complete before calling onDismiss
    setTimeout(() => {
      onDismiss();
      setIsAnimatingOut(false);
    }, 300); // Match animation duration
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Backdrop - very subtle, non-blocking */}
      <div 
        className={`fixed inset-0 z-40 transition-opacity duration-300 ${
          isAnimatingOut ? 'opacity-0' : 'opacity-100'
        }`}
        style={{ 
          background: 'rgba(0, 0, 0, 0.1)',
          pointerEvents: 'none' // Allow clicks through backdrop
        }}
      />

      {/* Popup Container */}
      <div className="fixed inset-0 z-50 pointer-events-none">
        <div className="flex items-start justify-center pt-16 sm:pt-20 md:pt-24 px-4">
          <Card 
            className={`
              relative max-w-md w-full pointer-events-auto
              bg-card/95 backdrop-blur-sm border-border/50 shadow-2xl
              transition-all duration-300 ease-out
              ${isAnimatingOut 
                ? 'opacity-0 scale-95 translate-y-[-10px]' 
                : 'opacity-100 scale-100 translate-y-0'
              }
            `}
            style={{
              background: 'rgba(10, 10, 10, 0.95)', // Match dark theme
              borderColor: 'rgba(230, 203, 142, 0.2)' // Subtle primary border
            }}
          >
            {/* Close Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="absolute right-2 top-2 h-8 w-8 p-0 hover:bg-primary/10 text-muted-foreground hover:text-foreground transition-colors"
              aria-label="Close popup"
            >
              <X className="h-4 w-4" />
            </Button>

            <CardContent className="p-6 pr-12">
              {/* Header with Icon */}
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 rounded-full bg-primary/10">
                  <Heart className="w-5 h-5 text-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">
                    Supporting Free Content
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    A quick note about our service
                  </p>
                </div>
              </div>

              {/* Main Message */}
              <div className="space-y-3 text-sm text-muted-foreground">
                <p className="leading-relaxed">
                  Hi there! 👋 We keep this streaming service completely free by showing 
                  a few ads that help cover our server costs and storage expenses.
                </p>
                
                <p className="leading-relaxed">
                  For the best experience and to support our free content, consider 
                  <span className="text-primary font-medium"> whitelisting our site</span> in 
                  your ad blocker settings.
                </p>

                <p className="text-xs text-muted-foreground/80 italic">
                  Don't worry - you can still use the site normally! This is just a friendly reminder. 😊
                </p>
              </div>

              {/* Optional Action Button */}
              <div className="mt-5 flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleDismiss}
                  className="text-xs border-primary/20 hover:border-primary/40 hover:bg-primary/5"
                >
                  Got it, thanks!
                </Button>
              </div>

              {/* Auto-close indicator */}
              <div className="mt-3 flex items-center justify-center gap-2 text-xs text-muted-foreground/60">
                <Info className="w-3 h-3" />
                <span>This message will auto-close in a few seconds</span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default AdBlockerAwarenessPopup;

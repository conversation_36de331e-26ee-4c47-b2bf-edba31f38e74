/**
 * Comprehensive Ad Blocker Detection System
 * 
 * This module provides utilities for detecting various types of ad blockers
 * including browser extensions, antivirus software blockers, and ad-blocking browsers.
 */

export interface AdBlockerInfo {
  name: string;
  type: 'extension' | 'browser' | 'antivirus' | 'unknown';
  detected: boolean;
  confidence: number; // 0-100
  detectionMethod: string;
  disableInstructions: string[];
  whitelistInstructions: string[];
}

export interface AdBlockerDetectionResult {
  hasAdBlocker: boolean;
  detectedBlockers: AdBlockerInfo[];
  totalConfidence: number;
  detectionTimestamp: number;
}

// Known ad blocker signatures and detection patterns
const AD_BLOCKER_SIGNATURES = {
  // Browser Extensions
  uBlockOrigin: {
    name: 'uBlock Origin',
    type: 'extension' as const,
    detectionMethods: [
      'elementHiding',
      'scriptBlocking',
      'networkBlocking',
      'domModification'
    ],
    disableInstructions: [
      'Click the uBlock Origin icon in your browser toolbar',
      'Click the large power button to disable uBlock Origin',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the uBlock Origin icon in your browser toolbar',
      'Click "No" under "Disable uBlock Origin for this site"',
      'Or click the "Trust this site" button',
      'Refresh the page to continue watching'
    ]
  },
  adBlockPlus: {
    name: 'Adblock Plus',
    type: 'extension' as const,
    detectionMethods: [
      'elementHiding',
      'scriptBlocking',
      'networkBlocking'
    ],
    disableInstructions: [
      'Click the Adblock Plus icon in your browser toolbar',
      'Click "Enabled on this site" to disable it',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the Adblock Plus icon in your browser toolbar',
      'Click "Enabled on this site" to turn it off',
      'Or go to Settings > Whitelisted websites and add this domain',
      'Refresh the page to continue watching'
    ]
  },
  adBlock: {
    name: 'AdBlock',
    type: 'extension' as const,
    detectionMethods: [
      'elementHiding',
      'scriptBlocking',
      'networkBlocking'
    ],
    disableInstructions: [
      'Click the AdBlock icon in your browser toolbar',
      'Click "Pause AdBlock" or "Don\'t run on this site"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the AdBlock icon in your browser toolbar',
      'Click "Don\'t run on pages on this domain"',
      'Refresh the page to continue watching'
    ]
  },
  ghostery: {
    name: 'Ghostery',
    type: 'extension' as const,
    detectionMethods: [
      'scriptBlocking',
      'networkBlocking',
      'trackerBlocking'
    ],
    disableInstructions: [
      'Click the Ghostery icon in your browser toolbar',
      'Toggle off "Ad Blocking" or "Enhanced Ad Blocking"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the Ghostery icon in your browser toolbar',
      'Click "Trust Site" or add this domain to trusted sites',
      'Refresh the page to continue watching'
    ]
  },
  // Browser Built-in Blockers
  braveShields: {
    name: 'Brave Shields',
    type: 'browser' as const,
    detectionMethods: [
      'userAgent',
      'scriptBlocking',
      'networkBlocking'
    ],
    disableInstructions: [
      'Click the Brave Shields icon in the address bar',
      'Toggle "Shields" to "Down" for this site',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the Brave Shields icon in the address bar',
      'Toggle "Shields" to "Down" for this site',
      'Or adjust individual shield settings',
      'Refresh the page to continue watching'
    ]
  },
  firefoxTracking: {
    name: 'Firefox Enhanced Tracking Protection',
    type: 'browser' as const,
    detectionMethods: [
      'userAgent',
      'scriptBlocking',
      'networkBlocking'
    ],
    disableInstructions: [
      'Click the shield icon in the Firefox address bar',
      'Click "Turn off Blocking for This Site"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Click the shield icon in the Firefox address bar',
      'Click "Turn off Blocking for This Site"',
      'Refresh the page to continue watching'
    ]
  },
  // Antivirus Software Blockers
  avastSecureLine: {
    name: 'Avast SecureLine/Web Shield',
    type: 'antivirus' as const,
    detectionMethods: [
      'networkBlocking',
      'scriptBlocking'
    ],
    disableInstructions: [
      'Open Avast Antivirus',
      'Go to Protection > Core Shields',
      'Temporarily disable "Web Shield"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Open Avast Antivirus',
      'Go to Protection > Core Shields > Web Shield Settings',
      'Add this website to exceptions',
      'Refresh the page to continue watching'
    ]
  },
  avgWebShield: {
    name: 'AVG Web Shield',
    type: 'antivirus' as const,
    detectionMethods: [
      'networkBlocking',
      'scriptBlocking'
    ],
    disableInstructions: [
      'Open AVG Antivirus',
      'Go to Computer > Web & Email Protection',
      'Temporarily disable "Web Shield"',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Open AVG Antivirus',
      'Go to Computer > Web & Email Protection > Web Shield',
      'Add this website to exceptions',
      'Refresh the page to continue watching'
    ]
  },
  kasperskyWebAntiVirus: {
    name: 'Kaspersky Web Anti-Virus',
    type: 'antivirus' as const,
    detectionMethods: [
      'networkBlocking',
      'scriptBlocking'
    ],
    disableInstructions: [
      'Open Kaspersky Security Center',
      'Go to Protection > Web Anti-Virus',
      'Temporarily disable protection',
      'Refresh the page to continue watching'
    ],
    whitelistInstructions: [
      'Open Kaspersky Security Center',
      'Go to Protection > Web Anti-Virus > Settings',
      'Add this website to trusted sites',
      'Refresh the page to continue watching'
    ]
  }
};

/**
 * Test for element hiding detection
 * Creates test elements that ad blockers typically hide
 */
function testElementHiding(): Promise<boolean> {
  return new Promise((resolve) => {
    const testElements = [
      'ads',
      'advertisement',
      'ad-banner',
      'google-ads',
      'adsystem',
      'ad-container',
      'sponsored-content'
    ];

    let hiddenCount = 0;
    const totalElements = testElements.length;

    testElements.forEach((className, index) => {
      const testDiv = document.createElement('div');
      testDiv.className = className;
      testDiv.style.position = 'absolute';
      testDiv.style.left = '-9999px';
      testDiv.style.width = '1px';
      testDiv.style.height = '1px';
      testDiv.innerHTML = '&nbsp;';
      
      document.body.appendChild(testDiv);

      // Check if element is hidden after a short delay
      setTimeout(() => {
        const computedStyle = window.getComputedStyle(testDiv);
        const isHidden = computedStyle.display === 'none' || 
                        computedStyle.visibility === 'hidden' ||
                        computedStyle.opacity === '0' ||
                        testDiv.offsetHeight === 0;

        if (isHidden) {
          hiddenCount++;
        }

        document.body.removeChild(testDiv);

        if (index === totalElements - 1) {
          // If more than 50% of test elements are hidden, likely ad blocker
          resolve(hiddenCount / totalElements > 0.5);
        }
      }, 100);
    });
  });
}

/**
 * Test for script blocking detection
 * Attempts to load known ad-serving scripts
 */
function testScriptBlocking(): Promise<boolean> {
  return new Promise((resolve) => {
    const testScripts = [
      'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js',
      'https://www.googletagservices.com/tag/js/gpt.js',
      'https://securepubads.g.doubleclick.net/tag/js/gpt.js'
    ];

    let blockedCount = 0;
    let completedTests = 0;

    testScripts.forEach((scriptUrl) => {
      const script = document.createElement('script');
      script.src = scriptUrl;
      script.async = true;

      const timeout = setTimeout(() => {
        blockedCount++;
        completedTests++;
        if (completedTests === testScripts.length) {
          resolve(blockedCount > 0);
        }
      }, 2000);

      script.onload = () => {
        clearTimeout(timeout);
        completedTests++;
        if (completedTests === testScripts.length) {
          resolve(blockedCount > 0);
        }
      };

      script.onerror = () => {
        clearTimeout(timeout);
        blockedCount++;
        completedTests++;
        if (completedTests === testScripts.length) {
          resolve(blockedCount > 0);
        }
      };

      document.head.appendChild(script);
      
      // Clean up
      setTimeout(() => {
        if (script.parentNode) {
          script.parentNode.removeChild(script);
        }
      }, 3000);
    });
  });
}

/**
 * Test for network request blocking
 * Attempts to make requests to known ad-serving domains
 */
function testNetworkBlocking(): Promise<boolean> {
  return new Promise((resolve) => {
    const testUrls = [
      'https://googleads.g.doubleclick.net/pagead/ads',
      'https://tpc.googlesyndication.com/simgad',
      'https://pagead2.googlesyndication.com/pagead/gen_204'
    ];

    let blockedCount = 0;
    let completedTests = 0;

    testUrls.forEach((url) => {
      const img = new Image();
      
      const timeout = setTimeout(() => {
        blockedCount++;
        completedTests++;
        if (completedTests === testUrls.length) {
          resolve(blockedCount > 0);
        }
      }, 2000);

      img.onload = () => {
        clearTimeout(timeout);
        completedTests++;
        if (completedTests === testUrls.length) {
          resolve(blockedCount > 0);
        }
      };

      img.onerror = () => {
        clearTimeout(timeout);
        blockedCount++;
        completedTests++;
        if (completedTests === testUrls.length) {
          resolve(blockedCount > 0);
        }
      };

      img.src = url + '?t=' + Date.now();
    });
  });
}

/**
 * Detect browser-specific ad blocking features
 */
function detectBrowserFeatures(): AdBlockerInfo[] {
  const detected: AdBlockerInfo[] = [];
  const userAgent = navigator.userAgent.toLowerCase();

  // Brave Browser Detection
  if (userAgent.includes('brave') || (window as any).navigator?.brave) {
    detected.push({
      name: AD_BLOCKER_SIGNATURES.braveShields.name,
      type: AD_BLOCKER_SIGNATURES.braveShields.type,
      detected: true,
      confidence: 90,
      detectionMethod: 'User Agent / Browser API',
      disableInstructions: AD_BLOCKER_SIGNATURES.braveShields.disableInstructions,
      whitelistInstructions: AD_BLOCKER_SIGNATURES.braveShields.whitelistInstructions
    });
  }

  // Firefox Enhanced Tracking Protection
  if (userAgent.includes('firefox') && (window as any).navigator?.doNotTrack) {
    detected.push({
      name: AD_BLOCKER_SIGNATURES.firefoxTracking.name,
      type: AD_BLOCKER_SIGNATURES.firefoxTracking.type,
      detected: true,
      confidence: 70,
      detectionMethod: 'User Agent / DNT Header',
      disableInstructions: AD_BLOCKER_SIGNATURES.firefoxTracking.disableInstructions,
      whitelistInstructions: AD_BLOCKER_SIGNATURES.firefoxTracking.whitelistInstructions
    });
  }

  return detected;
}

/**
 * Main ad blocker detection function
 * Runs multiple detection methods and returns comprehensive results
 */
export async function detectAdBlockers(): Promise<AdBlockerDetectionResult> {
  const detectedBlockers: AdBlockerInfo[] = [];
  let totalConfidence = 0;

  try {
    // Browser-specific detection (immediate)
    const browserBlockers = detectBrowserFeatures();
    detectedBlockers.push(...browserBlockers);

    // Run async detection tests
    const [elementHiding, scriptBlocking, networkBlocking] = await Promise.all([
      testElementHiding(),
      testScriptBlocking(),
      testNetworkBlocking()
    ]);

    // Analyze results and determine specific ad blockers
    if (elementHiding || scriptBlocking || networkBlocking) {
      let confidence = 0;
      let detectionMethods: string[] = [];

      if (elementHiding) {
        confidence += 30;
        detectionMethods.push('Element Hiding');
      }
      if (scriptBlocking) {
        confidence += 40;
        detectionMethods.push('Script Blocking');
      }
      if (networkBlocking) {
        confidence += 30;
        detectionMethods.push('Network Blocking');
      }

      // If we have high confidence but no specific browser blocker detected,
      // it's likely a browser extension
      if (confidence >= 70 && browserBlockers.length === 0) {
        detectedBlockers.push({
          name: 'Browser Extension Ad Blocker',
          type: 'extension',
          detected: true,
          confidence: Math.min(confidence, 95),
          detectionMethod: detectionMethods.join(', '),
          disableInstructions: [
            'Look for an ad blocker icon in your browser toolbar',
            'Click the icon and disable the ad blocker for this site',
            'Common ad blockers include uBlock Origin, Adblock Plus, AdBlock',
            'Refresh the page after disabling'
          ],
          whitelistInstructions: [
            'Look for an ad blocker icon in your browser toolbar',
            'Click the icon and add this site to your whitelist',
            'Look for options like "Don\'t run on this site" or "Trust this site"',
            'Refresh the page after whitelisting'
          ]
        });
      }
    }

    // Calculate total confidence
    totalConfidence = detectedBlockers.reduce((sum, blocker) => sum + blocker.confidence, 0) / Math.max(detectedBlockers.length, 1);

    return {
      hasAdBlocker: detectedBlockers.length > 0,
      detectedBlockers,
      totalConfidence,
      detectionTimestamp: Date.now()
    };

  } catch (error) {
    console.error('Ad blocker detection failed:', error);
    return {
      hasAdBlocker: false,
      detectedBlockers: [],
      totalConfidence: 0,
      detectionTimestamp: Date.now()
    };
  }
}

/**
 * Quick ad blocker check (faster, less comprehensive)
 * Useful for initial checks before loading video content
 */
export async function quickAdBlockerCheck(): Promise<boolean> {
  try {
    // Quick element hiding test
    const testDiv = document.createElement('div');
    testDiv.className = 'ads advertisement';
    testDiv.style.position = 'absolute';
    testDiv.style.left = '-9999px';
    document.body.appendChild(testDiv);

    await new Promise(resolve => setTimeout(resolve, 100));

    const isHidden = testDiv.offsetHeight === 0 || 
                    window.getComputedStyle(testDiv).display === 'none';

    document.body.removeChild(testDiv);

    return isHidden;
  } catch (error) {
    console.error('Quick ad blocker check failed:', error);
    return false;
  }
}

/**
 * Re-check ad blocker status
 * Used when user claims to have disabled their ad blocker
 */
export async function recheckAdBlockers(): Promise<boolean> {
  const result = await detectAdBlockers();
  return !result.hasAdBlocker;
}

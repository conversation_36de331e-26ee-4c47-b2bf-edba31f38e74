import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { ArrowLeft, Play, Settings, Monitor, Smartphone, Tablet } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import SecureVideoPlayer from '@/components/SecureVideoPlayer';
import { encodeVideoLinks } from '@/utils/videoSecurity';
import { scrollToTop } from '@/utils/scrollToTop';

export default function VideoPlayerDemo() {
  const [selectedDemo, setSelectedDemo] = useState('standard');

  // Different video link configurations for testing
  const demoConfigurations = {
    standard: {
      title: "Standard Configuration",
      description: "Multiple embed sources with standard settings",
      links: `https://www.youtube.com/embed/dQw4w9WgXcQ
https://player.vimeo.com/video/123456789
https://www.2embed.cc/embed/574475`,
      settings: {
        showPlayerSelection: true,
        enableDynamicAspectRatio: false,
        forceMobileFriendly: false,
        enableResponsive: true
      }
    },
    mobileFriendly: {
      title: "Mobile-Friendly Configuration",
      description: "Optimized for mobile devices with forced mobile-friendly ratios",
      links: `https://www.youtube.com/embed/ScMzIvxBSi4
https://www.dailymotion.com/embed/video/x123456`,
      settings: {
        showPlayerSelection: true,
        enableDynamicAspectRatio: false,
        forceMobileFriendly: true,
        enableResponsive: true
      }
    },
    dynamicAspect: {
      title: "Dynamic Aspect Ratio",
      description: "Automatically detects and adjusts aspect ratios based on platform",
      links: `https://www.youtube.com/embed/YQHsXMglC9A
https://streamtape.com/v/demo123/test-video.mkv
https://filemoon.to/e/demo456`,
      settings: {
        showPlayerSelection: true,
        enableDynamicAspectRatio: true,
        forceMobileFriendly: false,
        enableResponsive: true
      }
    },
    singlePlayer: {
      title: "Single Player Mode",
      description: "Single video source without player selection",
      links: `https://www.youtube.com/embed/gMxWhiHccOg`,
      settings: {
        showPlayerSelection: false,
        enableDynamicAspectRatio: false,
        forceMobileFriendly: false,
        enableResponsive: true
      }
    }
  };

  const currentConfig = demoConfigurations[selectedDemo as keyof typeof demoConfigurations];

  return (
    <div className="flex flex-col min-h-screen bg-background">
      <Header />
      
      {/* Added spacing after Header */}
      <div className="h-7 md:h-10" />

      <main className="flex-1 w-full max-w-7xl mx-auto px-3 sm:px-4 py-2 sm:py-4">
        {/* Demo Header */}
        <div className="mb-8 sm:mb-12">
          <div className="flex items-center gap-4 mb-6">
            <Link
              to="/admin"
              onClick={scrollToTop}
              className="inline-flex items-center gap-2 px-4 py-2 border border-border rounded-md hover:bg-muted transition-colors"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Admin
            </Link>
            <Badge variant="outline" className="bg-primary/10 text-primary border-primary/30">
              <Play className="w-3 h-3 mr-1" />
              Video Player Demo
            </Badge>
          </div>

          <div className="text-center mb-8">
            <h1 className="text-4xl sm:text-5xl md:text-6xl font-black font-mono text-primary drop-shadow-[0_2px_10px_rgba(34,197,94,0.4)] tracking-wide uppercase leading-tight mb-4">
              Video Player Integration Demo
            </h1>
            <p className="text-lg text-muted-foreground max-w-3xl mx-auto leading-relaxed">
              Test different video player configurations, responsive behavior, and embed link compatibility across various platforms and screen sizes.
            </p>
          </div>
        </div>

        {/* Configuration Selector */}
        <Card className="mb-8 bg-card/95 backdrop-blur-sm border-border/50 shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-primary">
              <Settings className="w-5 h-5" />
              Player Configuration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
              {Object.entries(demoConfigurations).map(([key, config]) => (
                <div
                  key={key}
                  className="relative"
                >
                  <Button
                    variant={selectedDemo === key ? "default" : "outline"}
                    className={`h-auto min-h-[140px] p-3 sm:p-4 flex flex-col items-start justify-start text-left w-full ${
                      selectedDemo === key
                        ? "bg-primary text-primary-foreground"
                        : "hover:bg-muted"
                    }`}
                    onClick={() => setSelectedDemo(key)}
                  >
                    <div className="w-full flex flex-col h-full">
                      <div className="font-medium mb-2 text-sm leading-tight w-full">
                        {config.title}
                      </div>
                      <div className="text-xs opacity-80 leading-relaxed break-words w-full flex-1">
                        {config.description}
                      </div>
                    </div>
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Video Player Demo */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Main Player */}
          <div className="lg:col-span-2">
            <Card className="bg-card/95 backdrop-blur-sm border-border/50 shadow-xl">
              <CardHeader>
                <CardTitle className="text-primary">{currentConfig.title}</CardTitle>
                <p className="text-sm text-muted-foreground">{currentConfig.description}</p>
              </CardHeader>
              <CardContent className="p-0">
                <SecureVideoPlayer
                  encodedVideoLinks={encodeVideoLinks(currentConfig.links)}
                  title={`Demo: ${currentConfig.title}`}
                  showPlayerSelection={currentConfig.settings.showPlayerSelection}
                  enableDynamicAspectRatio={currentConfig.settings.enableDynamicAspectRatio}
                  forceMobileFriendly={currentConfig.settings.forceMobileFriendly}
                  enableResponsive={currentConfig.settings.enableResponsive}
                  className="w-full"
                />
              </CardContent>
            </Card>
          </div>

          {/* Configuration Details */}
          <div className="space-y-4">
            <Card className="bg-card/95 backdrop-blur-sm border-border/50 shadow-xl">
              <CardHeader>
                <CardTitle className="text-sm text-primary">Current Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {Object.entries(currentConfig.settings).map(([key, value]) => (
                  <div key={key} className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground capitalize">
                      {key.replace(/([A-Z])/g, ' $1').trim()}
                    </span>
                    <Badge variant={value ? "default" : "secondary"} className="text-xs">
                      {value ? "Enabled" : "Disabled"}
                    </Badge>
                  </div>
                ))}
              </CardContent>
            </Card>

            <Card className="bg-card/95 backdrop-blur-sm border-border/50 shadow-xl">
              <CardHeader>
                <CardTitle className="text-sm text-primary">Video Sources</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {currentConfig.links.split('\n').map((link, index) => (
                    <div key={index} className="text-xs text-muted-foreground p-2 bg-muted/30 rounded border">
                      Player {index + 1}: {link.substring(0, 30)}...
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Responsive Testing */}
        <Card className="mb-8 bg-card/95 backdrop-blur-sm border-border/50 shadow-xl">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-primary">
              <Monitor className="w-5 h-5" />
              Responsive Design Testing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="desktop" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="desktop" className="flex items-center gap-2">
                  <Monitor className="w-4 h-4" />
                  Desktop
                </TabsTrigger>
                <TabsTrigger value="tablet" className="flex items-center gap-2">
                  <Tablet className="w-4 h-4" />
                  Tablet
                </TabsTrigger>
                <TabsTrigger value="mobile" className="flex items-center gap-2">
                  <Smartphone className="w-4 h-4" />
                  Mobile
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="desktop" className="mt-6">
                <div className="p-4 border border-border/50 rounded-lg">
                  <p className="text-sm text-muted-foreground mb-4">
                    Desktop view (1024px+): Full-width player with all controls visible
                  </p>
                  <div className="bg-muted/20 p-4 rounded border-2 border-dashed border-muted-foreground/30">
                    <div className="aspect-video bg-black/20 rounded flex items-center justify-center">
                      <span className="text-muted-foreground">Desktop Player Preview</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="tablet" className="mt-6">
                <div className="p-4 border border-border/50 rounded-lg">
                  <p className="text-sm text-muted-foreground mb-4">
                    Tablet view (768px-1024px): Responsive player with touch-friendly controls
                  </p>
                  <div className="max-w-md mx-auto bg-muted/20 p-4 rounded border-2 border-dashed border-muted-foreground/30">
                    <div className="aspect-video bg-black/20 rounded flex items-center justify-center">
                      <span className="text-muted-foreground text-sm">Tablet Player Preview</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
              
              <TabsContent value="mobile" className="mt-6">
                <div className="p-4 border border-border/50 rounded-lg">
                  <p className="text-sm text-muted-foreground mb-4">
                    Mobile view (320px-768px): Optimized aspect ratio and mobile-friendly controls
                  </p>
                  <div className="max-w-xs mx-auto bg-muted/20 p-4 rounded border-2 border-dashed border-muted-foreground/30">
                    <div className="aspect-video bg-black/20 rounded flex items-center justify-center">
                      <span className="text-muted-foreground text-xs">Mobile Player Preview</span>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Testing Instructions */}
        <Card className="mb-8 bg-card/95 backdrop-blur-sm border-border/50 shadow-xl">
          <CardHeader>
            <CardTitle className="text-primary">Testing Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-foreground mb-3">Functionality Tests</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Test player selection buttons (if enabled)</li>
                  <li>• Verify video loading and playback</li>
                  <li>• Check fullscreen functionality</li>
                  <li>• Test different embed link formats</li>
                  <li>• Verify ad blocker detection</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-foreground mb-3">Responsive Tests</h4>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Resize browser window to test breakpoints</li>
                  <li>• Test on actual mobile devices</li>
                  <li>• Verify touch controls work properly</li>
                  <li>• Check aspect ratio adjustments</li>
                  <li>• Test landscape/portrait orientations</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <section className="text-center mb-8">
          <h3 className="text-xl font-bold text-foreground mb-4">Quick Actions</h3>
          <div className="flex flex-wrap justify-center gap-4">
            <Link to="/admin" onClick={scrollToTop}>
              <Button className="bg-primary hover:bg-primary/90 text-primary-foreground">
                <Settings className="w-4 h-4 mr-2" />
                Return to Admin Panel
              </Button>
            </Link>
            <Link to="/admin/content-preview" onClick={scrollToTop}>
              <Button variant="outline" className="border-primary/30 text-primary hover:bg-primary/10">
                <Play className="w-4 h-4 mr-2" />
                View Content Preview
              </Button>
            </Link>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  ShieldAlert, 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle,
  Heart,
  Server,
  Loader2
} from 'lucide-react';
import { AdBlockerInfo, AdBlockerDetectionResult, recheckAdBlockers } from '@/utils/adBlockerDetection';

interface AdBlockerDetectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  detectionResult: AdBlockerDetectionResult;
  onRetry: () => void;
  onProceed: () => void;
}

const AdBlockerDetectionModal: React.FC<AdBlockerDetectionModalProps> = ({
  isOpen,
  onClose,
  detectionResult,
  onRetry,
  onProceed
}) => {
  const [isRechecking, setIsRechecking] = useState(false);
  const [recheckResult, setRecheckResult] = useState<boolean | null>(null);
  const [expandedBlocker, setExpandedBlocker] = useState<string | null>(null);

  const handleRecheck = async () => {
    setIsRechecking(true);
    setRecheckResult(null);
    
    try {
      const isClean = await recheckAdBlockers();
      setRecheckResult(isClean);
      
      if (isClean) {
        // Wait a moment to show success, then proceed
        setTimeout(() => {
          onProceed();
        }, 1500);
      }
    } catch (error) {
      console.error('Recheck failed:', error);
      setRecheckResult(false);
    } finally {
      setIsRechecking(false);
    }
  };

  const getBlockerTypeIcon = (type: AdBlockerInfo['type']) => {
    switch (type) {
      case 'extension':
        return <Shield className="w-4 h-4" />;
      case 'browser':
        return <ShieldAlert className="w-4 h-4" />;
      case 'antivirus':
        return <Server className="w-4 h-4" />;
      default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const getBlockerTypeBadgeVariant = (type: AdBlockerInfo['type']) => {
    switch (type) {
      case 'extension':
        return 'default';
      case 'browser':
        return 'secondary';
      case 'antivirus':
        return 'outline';
      default:
        return 'destructive';
    }
  };

  const toggleBlockerExpansion = (blockerName: string) => {
    setExpandedBlocker(expandedBlocker === blockerName ? null : blockerName);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto bg-card border-border">
        <DialogHeader className="space-y-4">
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-full bg-primary/10">
              <ShieldAlert className="w-6 h-6 text-primary" />
            </div>
            <div>
              <DialogTitle className="text-xl font-semibold text-foreground">
                Ad Blocker Detected
              </DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Please disable your ad blocker to continue watching
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Polite Message */}
          <Alert className="border-primary/20 bg-primary/5">
            <Heart className="w-4 h-4 text-primary" />
            <AlertDescription className="text-sm">
              <div className="space-y-2">
                <p className="font-medium text-foreground">
                  We understand ads can be annoying, but they help us keep this service free!
                </p>
                <p className="text-muted-foreground">
                  Our ads help cover server costs and storage expenses so we can continue providing 
                  free streaming content. Please consider disabling your ad blocker for this site.
                </p>
              </div>
            </AlertDescription>
          </Alert>

          {/* Detection Results */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium text-foreground">
                Detected Ad Blockers ({detectionResult.detectedBlockers.length})
              </h3>
              <Badge variant="outline" className="text-xs">
                {Math.round(detectionResult.totalConfidence)}% confidence
              </Badge>
            </div>

            <div className="space-y-3">
              {detectionResult.detectedBlockers.map((blocker, index) => (
                <div
                  key={index}
                  className="border border-border rounded-lg p-4 bg-card/50 hover:bg-card/80 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      {getBlockerTypeIcon(blocker.type)}
                      <div>
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-foreground">{blocker.name}</span>
                          <Badge 
                            variant={getBlockerTypeBadgeVariant(blocker.type)}
                            className="text-xs"
                          >
                            {blocker.type}
                          </Badge>
                        </div>
                        <p className="text-xs text-muted-foreground">
                          Detected via: {blocker.detectionMethod}
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => toggleBlockerExpansion(blocker.name)}
                      className="text-primary hover:text-primary/80"
                    >
                      {expandedBlocker === blocker.name ? 'Hide' : 'Show'} Instructions
                    </Button>
                  </div>

                  {expandedBlocker === blocker.name && (
                    <div className="mt-4 space-y-4 border-t border-border pt-4">
                      {/* Disable Instructions */}
                      <div>
                        <h4 className="font-medium text-foreground mb-2 flex items-center gap-2">
                          <AlertTriangle className="w-4 h-4 text-orange-500" />
                          How to Disable
                        </h4>
                        <ol className="space-y-1 text-sm text-muted-foreground">
                          {blocker.disableInstructions.map((instruction, idx) => (
                            <li key={idx} className="flex gap-2">
                              <span className="text-primary font-medium min-w-[1.5rem]">
                                {idx + 1}.
                              </span>
                              <span>{instruction}</span>
                            </li>
                          ))}
                        </ol>
                      </div>

                      {/* Whitelist Instructions */}
                      <div>
                        <h4 className="font-medium text-foreground mb-2 flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          How to Whitelist This Site (Recommended)
                        </h4>
                        <ol className="space-y-1 text-sm text-muted-foreground">
                          {blocker.whitelistInstructions.map((instruction, idx) => (
                            <li key={idx} className="flex gap-2">
                              <span className="text-primary font-medium min-w-[1.5rem]">
                                {idx + 1}.
                              </span>
                              <span>{instruction}</span>
                            </li>
                          ))}
                        </ol>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Recheck Section */}
          <div className="space-y-4">
            <div className="border-t border-border pt-4">
              <h3 className="text-lg font-medium text-foreground mb-3">
                Ready to Continue?
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                After disabling or whitelisting your ad blocker, click the button below to verify and continue watching.
              </p>

              <div className="flex flex-col sm:flex-row gap-3">
                <Button
                  onClick={handleRecheck}
                  disabled={isRechecking}
                  className="flex-1 bg-primary hover:bg-primary/90 text-primary-foreground"
                >
                  {isRechecking ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="w-4 h-4 mr-2" />
                      I've Disabled My Ad Blocker
                    </>
                  )}
                </Button>
                
                <Button
                  variant="outline"
                  onClick={onRetry}
                  className="flex-1"
                >
                  Retry Detection
                </Button>
              </div>

              {/* Recheck Result */}
              {recheckResult !== null && (
                <div className="mt-4">
                  {recheckResult ? (
                    <Alert className="border-green-500/20 bg-green-500/5">
                      <CheckCircle className="w-4 h-4 text-green-500" />
                      <AlertDescription className="text-green-700 dark:text-green-400">
                        Great! No ad blockers detected. Redirecting to video...
                      </AlertDescription>
                    </Alert>
                  ) : (
                    <Alert className="border-orange-500/20 bg-orange-500/5">
                      <AlertTriangle className="w-4 h-4 text-orange-500" />
                      <AlertDescription className="text-orange-700 dark:text-orange-400">
                        Ad blocker still detected. Please make sure you've disabled it completely and refreshed the page.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Additional Help */}
          <div className="border-t border-border pt-4">
            <details className="group">
              <summary className="cursor-pointer text-sm font-medium text-foreground hover:text-primary transition-colors">
                Need more help? Click here for additional troubleshooting
              </summary>
              <div className="mt-3 space-y-2 text-sm text-muted-foreground">
                <p>• Try refreshing the page after disabling your ad blocker</p>
                <p>• Clear your browser cache and cookies for this site</p>
                <p>• Disable any privacy-focused browser extensions temporarily</p>
                <p>• Check if your antivirus software has web protection enabled</p>
                <p>• Try using a different browser or incognito/private mode</p>
              </div>
            </details>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AdBlockerDetectionModal;

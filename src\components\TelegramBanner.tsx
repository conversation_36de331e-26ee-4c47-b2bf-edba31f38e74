
import React from "react";

// Telegram channel link
const telegramLink = "https://t.me/thestreamdb";

export default function TelegramBanner() {
  return (
    <section
      className="mb-4 mx-auto max-w-3xl"
      style={{
        background: "none",
        boxShadow: "none",
        border: "none",
        padding: 0,
        transform: "scale(1)",
      }}
    >
      {/* Match Cloudflare banner structure exactly */}
      <div
        className="relative rounded-xl w-full mx-auto overflow-hidden"
        style={{ minHeight: 110 }}
      >
        <div
          className="relative z-10 px-2 py-5 flex flex-col items-center justify-center"
          style={{
            minHeight: 150, // Match Cloudflare banner's actual content height
          }}
        >
          {/* Clickable area for the entire content */}
          <a
            href={telegramLink}
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full group cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-background rounded-xl"
            aria-label="Join our Telegram channel for latest uploads, exclusive content, and early access"
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                e.preventDefault();
                window.open(telegramLink, '_blank', 'noopener,noreferrer');
              }
            }}
          >
            {/* Telegram Banner Image */}
            <img
              src="/telegram-banner.png"
              alt="Join our Telegram channel for latest uploads, exclusive content, and early access"
              className="w-full rounded-xl transition-transform duration-200 group-hover:scale-[1.02]"
              style={{
                height: "150px", // Match content area minHeight for proper card height
                width: "100%",
                objectFit: "cover", // Full coverage without black space or gaps
                objectPosition: "center",
                display: "block",
                background: "linear-gradient(to right, #2fbdfa 0%, #2da1fa 100%)", // Fallback gradient background
              }}
              loading="lazy"
              onError={(e) => {
                // Fallback styling if image fails to load
                const target = e.target as HTMLImageElement;
                target.style.background = "linear-gradient(to right, #2fbdfa 0%, #2da1fa 100%)";
                target.style.height = "150px";
                target.alt = "Telegram Banner - Image not available";
              }}
            />
          </a>
        </div>
      </div>
    </section>
  );
}
